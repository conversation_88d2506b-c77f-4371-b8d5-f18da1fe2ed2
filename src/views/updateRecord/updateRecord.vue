<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { getUpdateLogs } from '@/api/tucao'
import { IconCalendar, IconTag } from '@arco-design/web-vue/es/icon'
import dayjs from 'dayjs'

async function toJumpTuTao(postId: number) {
  utools.shellOpenPath(`https://support.qq.com/products/660084/post/${postId}/`)
}
const updateLog = ref();
onMounted(async () => {
  const data = await getUpdateLogs();
  updateLog.value = data;
  console.log(JSON.stringify(data));
});
const verContentRef = ref<HTMLDivElement>();
function menuClick(key: string) {
  const keys = key.split('/');
  if (keys.length == 2) {
    const searchKey = keys[1] === '0' ? keys[0] : key;
    console.log(keys[1], searchKey);
    const element = document.getElementById(searchKey);
    console.log('element', element);
    const target = element!.offsetTop;
    console.log(target);
    if (verContentRef.value) {
      verContentRef.value.scrollTop = target - 40;
      console.log('111', element);
      document.getElementById(key)!.classList.add('pulse');
      setTimeout(() => {
        document.getElementById(key)!.classList.remove('pulse');
      }, 1000);
    }
  }
}
</script>
<template>
  <div class="u-main-content">
    <a-page-header :show-back="false">
      <template #title>更新记录</template>
      <template #subtitle>
        可以记录了本插件迭代记录, 如果想了解最新版本更新什么内容可以到这里查看
      </template>
    </a-page-header>
    <div class="u-main-content-inner">
      <div v-if="updateLog" class="update-content">
        <a-menu :style="{ width: '100%', height: '100%' }" @menu-item-click="menuClick">
          <a-sub-menu v-for="item in updateLog.data" :key="item.id">
            <template #title>
              <a-tag color="arcoblue" size="small" style="border-radius: 10px">
                {{ item.content.version }}
              </a-tag>
              <div style="min-width: 90px"> {{ item.list_id.split(' ')[0] }}</div>
            </template>
            <a-menu-item
              v-for="(content, index) in item.content.content"
              :key="`${item.id}/${index}`"
            >
              <span>{{ content.title }}</span>
            </a-menu-item>
          </a-sub-menu>
        </a-menu>
        <div ref="verContentRef" class="ver-content-wrapper">
          <div v-for="item in updateLog.data" :id="item.id" :key="item.id" class="ver-content">
            <div class="version-title u-fx ver">
              <icon-calendar />
              <div class="time">
                {{ dayjs(item.list_id).locale('zh-cn').format('YYYY.MM.DD dddd') }}
              </div>
              <a-tag color="arcoblue" size="small" style="border-radius: 10px">
                <template #icon>
                  <icon-tag />
                </template>
                {{ item.content.version }}
              </a-tag>
            </div>
            <div class="u-fx u-fac publisher">
              <div class="u-fx u-fac">
                <div>
                  <a-avatar :size="28">
                    <img alt="avatar" :src="item.avatar_url" />
                  </a-avatar>
                </div>
                <div :class="[item.is_admin ? 'admin' : '']">
                  {{ item.nick_name }}
                </div>
                <div class="publish-time">发布于{{ item.time }}</div>
              </div>
            </div>
            <div class="content">
              <div
                v-for="(content, index) in item.content.content"
                :id="`${item.id}/${index}`"
                :key="`${item.id}/${index}`"
                class="animated infinite"
              >
                <h2>{{ content.title }}</h2>
                <div v-html="content.detail"></div>
              </div>
            </div>
            <div
              v-if="item.content.thank_post_id_list && item.content.thank_post_id_list.length"
              class="thank-wrapper"
            >
              <div class="thank">
                <div>本次更新，感谢以下同学建议</div>
                <div>
                  <div
                    v-for="(user, index) in item.content.thank_post_id_list"
                    :key="index"
                    class="fc"
                    style="gap: 10px"
                  >
                    <a-avatar :size="24">
                      <img :src="user.avatarUrl" />
                    </a-avatar>
                    <span> {{ user.nickName }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
@height: calc(100vh - 136px);
.u-main-content-inner {
  display: grid;
  grid-template-rows: 1fr;
  box-sizing: border-box;
  padding: 10px;
}
.big-event {
  background: #ffffff;
  padding: 10px;
  overflow-x: auto;
  border-radius: 10px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  :deep(.arco-timeline-direction-horizontal) {
    //gap: 18px;
  }
  :deep(.arco-timeline-item-horizontal-top) {
    padding-left: 10px;
    flex: none;
  }
}
.update-content {
  display: grid;
  grid-template-columns: 200px 1fr;
  grid-template-rows: 1fr;
  gap: 8px;
  :deep(.arco-menu) {
    max-height: @height;
    ::-webkit-scrollbar {
      display: none;
    }
  }
  :deep(.arco-menu-light .arco-menu-inline-content .arco-menu-selected) {
    background: var(--main-background) !important;
    margin: 0 8px;
  }
  :deep(.arco-menu-item) {
    border-radius: 10px;
  }
  > div {
    border-radius: 10px;
    box-shadow: rgba(0, 0, 0, 0.04) 0px 3px 5px;
  }
  > div:last-child {
    background: var(--utools-background);
    box-sizing: border-box;
    padding: 10px;
  }
}
:deep(.arco-menu-inner) {
  padding: 2px;
}
:deep(.arco-menu-light .arco-menu-inline-header) {
  background: transparent !important;
}
:deep(.arco-menu-inline-header) {
  display: flex;
  align-items: center;
  gap: 4px;
}
.ver-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 66px;
  max-height: @height;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    display: none;
  }
}
.ver-content {
  .publisher {
    display: flex;
    justify-content: space-between;
    > div:first-child {
      gap: 5px;
    }
    .admin {
      color: #e74c3c;
      font-weight: 600;
    }
    .publish-time {
      font-size: 12px;
      color: #bdc3c7;
    }
  }
  .ver {
    font-size: 26px;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
    .time {
      font-size: 18px;
      font-weight: 600;
    }
  }
  .content {
    padding: 0px 10px 0;
    user-select: none;
  }
  h2 {
    font-size: 18px;
    margin: 18px 0 18px;
  }
  :deep(ol li) {
    margin-left: 20px;
    line-height: 2rem;
  }

}
.action {
  display: flex;
  justify-content: end;
  font-size: 18px;
  cursor: pointer;
}
.thank-wrapper {
  display: flex;
  justify-content: center;
}
.thank {
  width: 90%;
  margin-top: 10px;
  background: #f3f3f3;
  padding: 12px;
  border-radius: 10px;
  color: #2c2c2c;
  > div:first-child {
    font-size: 12px;
    padding-bottom: 8px;
    border-bottom: 1.5px dashed #e7e7e7;
  }
  > div:last-child {
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 20px;
    span {
      font-size: 13px;
      background: linear-gradient(135deg, #fd6e6a, #ffc600);
      font-weight: 600;
      -webkit-background-clip: text;
      color: transparent;
    }
  }
}

/*base code*/
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}
/*the animation definition*/
/*base code*/
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}
/*the animation definition*/
@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes pulse {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    -ms-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    -ms-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    -ms-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse;
}
</style>
