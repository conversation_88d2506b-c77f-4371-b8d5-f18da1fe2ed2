<script setup lang="tsx">
import { computed, h, nextTick, onMounted, ref } from 'vue'
import { App, ZoomEvent } from 'leafer-ui'
import '@leafer-in/editor'
import '@leafer-in/text-editor'
// 导入组件样式
import './components/toolbox/toolbox.less'
import ScreenshotFunctions from '@/leaferApp/ScreenshotCapacity'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import LeaferInstallCapacity from '@/leaferApp/LeaferInstallCapacity'
import HtmlDomConstants from '@/core/constants/HtmlDomConstants'
// import { Button, ButtonGroup, Checkbox } from '@arco-design/web-vue'
import { Button, Checkbox } from 'tdesign-vue-next';
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import LeaferHistoryList from '@/leaferApp/LeaferHistoryList'
import { FunMouseFunCode } from '@/stores/KeyboardConfigStore/store/FunMouseConfigStore'
import { useSettingUserStore } from '@/stores/SettingUserStore'
import { ScreenShotContextMenu } from '@/components/contextMenu/index'
import ToolboxBar from '@/components/ToolboxBar/ToolboxBar.vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import type { ToolBoxBarInstance } from '@/components/ToolboxBar/types'
import KeyboardsConstants from '@/core/constants/KeyboardsConstants'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import { OcrSuccessEvent } from '@/events/OcrSuccessEvent'
import OcrTool from '@/components/toolbox/OcrTool/OcrTool'
import hotkeys from 'hotkeys-js'
import { useEventListener } from '@vueuse/core'

// 为了加载配置
useToolConfigStore()
const contentRef = ref<HTMLDivElement>()
let app: App | null = null
const settingUserStore = useSettingUserStore()
const currentToolBoxStore = useCurrentToolBoxStore()
// 缩放比例
const zoomRatio = ref(100)
const toolBoxBarRef = ref<ToolBoxBarInstance>()
nextTick(() => {
  app = new App({
    move: {
      disabled: true,
    },
    view: contentRef.value,
    ground: { type: 'draw' },
    wheel: {
      zoomSpeed: settingUserStore.mouseWheelZoom ? 0.02 : 0.08,
      zoomMode: settingUserStore.mouseWheelZoom ? 'mouse' : false,
    },
    editor: {
      rect: {
        stroke: settingUserStore.generalSetting.toolSelectedBorderColor,
        strokeWidth: settingUserStore.generalSetting.toolSelectedBorderSize,
      },
    },
  })
  app.zoomLayer = app.tree
  // 历史列表
  window.leaferHistoryList = new LeaferHistoryList(app)
  LeaferInstallCapacity.registerApi(app)
  currentToolBoxStore.registerApp(app)
  // 这里工具加载移动到场景加载那边了, 因为那边会进行重新加载工具
  // src/leaferApp/LeaferInstallCapacity.ts:reloadSceneApi
  toolBoxBarRef.value?.loadToolList(app)
  LeaferInstallCapacity.loadBackground(app)
  LeaferInstallCapacity.loadEditorSelectImposeAction(app)
  LeaferInstallCapacity.installAllGroup(app)
  LeaferInstallCapacity.installDeleteElement(app)
  LeaferInstallCapacity.installZoom(app)
  app.tree.on(ZoomEvent.ZOOM, () => {
    zoomRatio.value = Math.round((app.zoomLayer.scale as number) * 100)
  })
  LeaferInstallCapacity.installPastePicture(app)
  LeaferInstallCapacity.installCopyPicture(app)
})
ScreenshotFunctions.switchToolBarRegisterShortcut()

function screenshotBorderStyle(mouse = false) {
  const contentWrapper = document.getElementById('contentWrapper')
  const mode = contentWrapper.getAttribute('data-mode')
  if (mode === 'mini') {
    return
  }
  const { screenshotBorder, screenshotBorderColor } = settingUserStore.generalSetting
  const color = screenshotBorderColor.replace('rgb(', '').replace(')', '')
  switch (screenshotBorder) {
    case 'color':
      if (mouse) {
        contentWrapper.style.border = `4px solid rgba(${color}, 0.8)`
      } else {
        contentWrapper.style.border = `4px solid rgba(${color}, 0.5)`
      }
      break
    case 'shadow':
      contentWrapper.style.margin = `4px`
      if (mouse) {
        contentWrapper.style.boxShadow = `rgba(${color}, 0.6) 0px 0px 0px 3px`
      } else {
        contentWrapper.style.boxShadow = `rgba(53, 53, 56, 0.2) 0px 0px 6px, rgba(53, 53, 56, 0.2) 0px 1px 6px`
      }
      break
    case 'none':
      contentWrapper.style.border = '4px solid transparent'
      break
  }
}
nextTick(() => {
  screenshotBorderStyle()
})

function handleMouseenter() {
  screenshotBorderStyle(true)
  const contentWrapper = document.getElementById('contentWrapper')
  const mode = contentWrapper.getAttribute('data-mode')
  if (mode === 'mini') {
    return
  }

  document.getElementById('right-action').style.display = 'flex'
  document.getElementById('right-bottom-action').style.display = 'flex'
}
function handleMouseleave() {
  screenshotBorderStyle()
  document.getElementById('right-action').style.display = 'none'
  document.getElementById('right-bottom-action').style.display = 'none'
}
// 双击关闭窗口
const keyboardConfigStore = useKeyboardConfigStore()
onMounted(() => {
  document.getElementById('contentWrapper').addEventListener('dblclick', (e) => {
    setTimeout(() => {
      if (window.tempData.DOUBLE_TAP) {
        return
      }
      const target = e.target as HTMLElement
      if (target.hasAttribute('stopClose')) {
        return
      }
      const funMouseConfig = keyboardConfigStore.getFunMouseConfig(FunMouseFunCode.COPY_LOSE)
      if (!funMouseConfig.enable) {
        return
      }
      if (!funMouseConfig.tipsEnable) {
        ScreenshotCapacity.exportScreenshotDataUrl(app).then((base64) => {
          if (settingUserStore.doubleClickCloseCopy) {
            utools.copyImage(base64)
          }
          ScreenshotCapacity.screenshotClose()
        })
        return
      }

      if (app.editor.target) {
        // 存在选择元素
        return
      }

      let modalValue = false
      ScreenshotCapacity.showConfirmNotification('info', {
        id: 'closeScreenshot',
        title: '关闭提醒',
        content: () => (
          <div>
            <div>是否复制并关闭截图</div>
            <div class="u-fx" style={{ 'padding-top': '5px' }}>
              <Checkbox
                onChange={(value) => {
                  modalValue = value as boolean
                }}
              >
                不再提醒
              </Checkbox>
            </div>
            <div class="u-fx gap-1"
                 style={{ 'padding-top': '5px', 'justify-content': 'flex-end' }}>
              <Button
                size="small"
                theme="default"
                onClick={() => {
                  console.log('modalValue.value', modalValue)
                  ScreenshotCapacity.showTipsNotification('info', {
                    id: 'closeScreenshot',
                    title: '关闭提醒',
                    content: '1',
                    duration: 1,
                  })
                }}
              >
                取消
              </Button>
              <Button
                size="small"
                theme="primary"
                onClick={() => {
                  keyboardConfigStore.setFunMouseConfig(
                    FunMouseFunCode.COPY_LOSE,
                    'tipsEnable',
                    !modalValue,
                  )
                  ScreenshotCapacity.exportScreenshotDataUrl(app).then((base64) => {
                    if (settingUserStore.doubleClickCloseCopy) {
                      utools.copyImage(base64)
                    }
                    ScreenshotCapacity.screenshotClose()
                  })
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        )
      })
    }, 200)
  })
})

const dingTopStatus = ref(true)
function handleDingTool() {
  const toolBarShow = document.getElementById('toolboxContainer').style.opacity === '1'
  if (toolBarShow) {
    ScreenshotCapacity.setToolBarShow(false);
  }
  window.winHelper.getCurrentWindow().setAlwaysOnTop(!dingTopStatus.value, 'modal-panel')
  dingTopStatus.value = window.winHelper.getCurrentWindow().isAlwaysOnTop()
  if (toolBarShow) {
    setTimeout(() => {
      ScreenshotCapacity.setToolBarShow(true)
    })
  }
}

// tab 切换工具条
const { getDefaultFuncKeyboardConfig } = useKeyboardConfigStore()
const switchToolBarKeyboard = computed(() => {
  return getDefaultFuncKeyboardConfig(KeyboardsConstants.SWITCHOVER_TOOLBAR)
})
const isMac = computed(() => utools.isMacOS())
function handleSwitchToolBar() {
  const toolboxContainer = document.getElementById('toolboxContainer')
  if (toolboxContainer.style.opacity === '1') {
    currentToolBoxStore.setCurrentToolBoxByCode('screenshotDrag')
  }
  if (toolboxContainer) {
    ScreenshotCapacity.setToolBarShow(toolboxContainer.style.opacity !== '1')
  }
}
const rightBottomActionRef = ref()

function handleRecoveryRatio() {
  const currentScaleRatio = (app.zoomLayer.scale as number) * 100
  const scaleRatio = 100 / currentScaleRatio
  app.interaction.zoom({ x: 100, y: 100, scale: scaleRatio })
}

const ocrDisplay = ref(0)
onMounted(() => {
  window.addEventListener(OcrSuccessEvent.OCR_SUCCESS, () => {
    ocrDisplay.value = 1
  })
})
function switchoverOcrDisplay() {
  const tool = currentToolBoxStore.getToolByCode<OcrTool>('ocr')
  if (ocrDisplay.value === 1) {
    tool.platformInstance.hide()
    ocrDisplay.value = 2
  } else {
    tool.platformInstance.show()
    ocrDisplay.value = 1
  }
}
function handlePickerColor() {
  utools.screenColorPick(({ hex, rgb }) => {
    utools.copyText(hex)
    ScreenshotCapacity.showConfirmNotification('info', {
      duration: 5000,
      content: () => (
        <div>
          已复制: {hex}
          <div class="u-fx u-mt10" style={{ justifyContent: 'flex-end' }}>
            <Button
              size="small"
              onClick={async () => {
                utools.redirect(['颜色助手', '颜色'], {
                  type: 'text',
                  data: hex,
                })
              }}
            >
              跳转颜色助手
            </Button>
          </div>
        </div>
      ),
    })
  })
}
const mousePosition = ref<MouseEvent>()
useEventListener('mousemove', (e: MouseEvent) => {
  mousePosition.value = e
})
onMounted(() => {
  hotkeys('r', (e) => {
    const element = document.querySelector('#contentWrapper') as HTMLDivElement
    if (element.style.width !== '60px') {
      element.setAttribute('data-mode', 'mini')
      if (mousePosition.value.target instanceof HTMLDivElement) {
        const contentWrapper = mousePosition.value.target.closest('#contentWrapper')
        if (contentWrapper) {
          const content = document.getElementById('content')
          // 获取容器的边界（不包含边框）
          const containerRect = contentWrapper.getBoundingClientRect()
          const containerWidth = contentWrapper.clientWidth
          const containerHeight = contentWrapper.clientHeight
          // 获取边框宽度
          const computedStyle = getComputedStyle(contentWrapper)
          const borderLeft = parseInt(computedStyle.borderLeftWidth) || 0
          const borderTop = parseInt(computedStyle.borderTopWidth) || 0
          // 计算相对于容器内容区域的鼠标位置
          const relativeX = mousePosition.value.x - containerRect.left - borderLeft
          const relativeY = mousePosition.value.y - containerRect.top - borderTop
          // 检查是否超出边界，如果超出则调整移动距离
          let adjustedX = relativeX
          let adjustedY = relativeY
          // 如果鼠标靠近左边界
          if (relativeX < 32) {
            adjustedX = 32
          }
          // 如果鼠标靠近右边界
          else if (relativeX > containerWidth - 32) {
            adjustedX = containerWidth - 32
          }
          // 如果鼠标靠近上边界
          if (relativeY < 32) {
            adjustedY = 32
          }
          // 如果鼠标靠近下边界
          else if (relativeY > containerHeight - 32) {
            adjustedY = containerHeight - 32
          }
          content.style.transform = `translate(${-adjustedX + 32}px, ${-adjustedY + 32}px)`
        }
      }
      const { width, height } = element.style
      const containerRect = element.getBoundingClientRect()
      const containerWidth = containerRect.width
      const containerHeight = containerRect.height
      console.log(containerWidth, containerRect.left)
      const border = element.style.border
      element.setAttribute(
        'data-original',
        JSON.stringify({
          width,
          height,
          border,
          containerWidth: containerWidth + containerRect.left,
          containerHeight: containerHeight + containerRect.top,
        }),
      )
      const { screenshotMiniBorder, screenshotMiniBorderColor } = settingUserStore.generalSetting
      if (screenshotMiniBorder) {
        const color = screenshotMiniBorderColor.replace('rgb(', '').replace(')', '')
        element.style.border = `4px solid rgba(${color}, 0.6)`
      }
      // 缩小框的宽度和高度, 这里可以进行调整
      const minWidth = 60
      const minHeight = 60
      const cursor = utools.getCursorScreenPoint()
      // 计算边框
      nextTick(() => {
        const containerRect = element.getBoundingClientRect()
        const borderWidth = (containerWidth - element.clientWidth) * 2
        const broderHeight = (containerHeight - element.clientHeight) * 2
        console.log((minWidth + borderWidth) / 2, (minHeight + broderHeight) / 2)
        element.style.width = `${minWidth}px`
        element.style.height = `${minHeight}px`
        window.winHelper.getCurrentWindow().setBounds({
          x: cursor.x - (minWidth + borderWidth) / 2,
          y: cursor.y - (minHeight + broderHeight) / 2,
          // 这里计算宽度, containerRect.left 和 containerRect.top 计算内容的因为 Mac 窗口默认有圆角有空白的距离所以要加上
          width: minWidth + containerRect.left + borderWidth,
          height: minHeight + containerRect.top + broderHeight,
        })
        element.classList.add('win-drag')
        document.getElementById('right-action').style.display = 'none'
        document.getElementById('right-bottom-action').style.display = 'none'
        document.getElementById('qrcode').style.display = 'none'
        document.getElementById('translate').style.display = 'none'
        document.getElementById('ocr').style.display = 'none'
        if (currentToolBoxStore.getCurrentToolBoxCode !== 'screenshotDrag') {
          currentToolBoxStore.setCurrentToolBoxByCode('screenshotDrag')
        }
      })
    } else {
      const content = document.getElementById('content')
      element.setAttribute('data-mode', 'default')
      content.style.transform = ''
      const { width, height, containerWidth, containerHeight, border } = JSON.parse(
        element.getAttribute('data-original'),
      )
      element.style.width = width
      element.style.height = height
      element.style.border = border
      const cursor = utools.getCursorScreenPoint()
      window.winHelper.getCurrentWindow().setBounds({
        x: Math.round(cursor.x - containerWidth / 2),
        y: Math.round(cursor.y - containerHeight / 2),
        width: Math.floor(containerWidth),
        height: Math.floor(containerHeight),
      })
      element.classList.remove('win-drag')
      document.getElementById('right-action').style.display = 'flex'
      document.getElementById('right-bottom-action').style.display = 'flex'
      document.getElementById('qrcode').style.display = 'block'
      document.getElementById('translate').style.display = 'block'
      document.getElementById('ocr').style.display = 'block'
      ScreenshotCapacity.setToolBarShow(true)
    }
  })
})
</script>

<template>
  <div id="app">
    <div id="contentWrapper" @mouseenter="handleMouseenter" @mouseleave="handleMouseleave">
      <div id="qrcode"></div>
      <div id="ocr"></div>
      <div id="translate"></div>
      <div id="ocrResult">
        <div @click.self.stop @dblclick.self.stop id="ocrResultInner" contenteditable></div>
      </div>
      <div id="right-action" class="u-fx u-fc u-fac u-gap5">
        <div
          v-if="!settingUserStore.imageToolsDisabledList.includes('zoomRatio')"
          class="scale-action"
          :class="dingTopStatus ? ['active'] : []"
          @click="handleRecoveryRatio"
          stopClose
        >
          <div>{{ zoomRatio }}%</div>
        </div>
        <div
          v-if="!settingUserStore.imageToolsDisabledList.includes('top')"
          class="u-fx u-fc u-fac u-pointer ding-action"
          :class="dingTopStatus ? ['active'] : []"
          @click.stop="handleDingTool"
          stopClose
        >
          <div class="i-u-ding w-4 h-4" stopClose />
        </div>
      </div>
      <div id="right-bottom-action" ref="rightBottomActionRef" class="u-fx u-fc u-fac u-gap5">
        <div
          class="btn u-pointer"
          @click="handlePickerColor"
          v-if="!settingUserStore.imageToolsDisabledList.includes('pickerColor')"
        >
          <div class="i-u-picker-color w-4 h-4"></div>
        </div>

        <div v-if="ocrDisplay"
             class="btn u-pointer"
             @click="switchoverOcrDisplay"
             stopClose>
          <div :class="ocrDisplay === 1 ? 'preview-open' : 'preview-close'"
               class="w-4 h-4"
               stopClose/>
        </div>
        <t-tooltip
          v-if="!settingUserStore.imageToolsDisabledList.includes('tab')"
          :popup-container="rightBottomActionRef">
          <template #content>
            <span class="u-font-size-smail" style="user-select: none">
              <span v-if="switchToolBarKeyboard.ctrl" style="padding-right: 2px">
                <span v-if="isMac">
                  <div class="i-u-command w-4 h-4" />
                </span>
                <span v-else>ctrl</span>
              </span>
              <span>{{ switchToolBarKeyboard.key }}</span>
            </span>
          </template>
          <div class="btn u-pointer" @click="handleSwitchToolBar"
               stopClose>
            <div class="i-u-switch w-4 h-4"
                 stopClose />
          </div>
        </t-tooltip>
      </div>
      <div id="screenshotDrag">
        <div class="drag"></div>
        <div class="drag-title">
          <div class="i-u-hand-drag" />
        </div>
      </div>
      <ScreenShotContextMenu>
        <div :id="HtmlDomConstants.ScreenshotContainer"
             ref="contentRef"></div>
      </ScreenShotContextMenu>
    </div>
    <ToolboxBar ref="toolBoxBarRef" />
  </div>
</template>

<style lang="less">
#screenshotDrag {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.3); /* 半透明背景 */
  backdrop-filter: blur(8px); /* 背景模糊 */
  z-index: 99;
  display: none;
  .drag-title {
    color: #1890ff;
    padding-top: 2px;
    position: absolute;
    left: 0;
    top: 0;
    line-height: 20px;
    width: 100%;
    text-align: center;
  }
  .drag {
    width: calc(100% - 100px);
    height: 20px;
    -webkit-app-region: drag;
  }
}
body {
  overflow: hidden;
}
#ocr,
#translate {
  .show {
    &::selection {
      color: #ffffff;
      background: rgba(73, 148, 196, 0.6);
    }
  }
  .hide {
    &::selection {
      color: transparent;
      font-size: 1px;
      background: rgba(73, 148, 196, 0.6);
    }
  }
}
#qrcode {
}
</style>
<style lang="less" scoped>
body[os='mac'] {
  #app {
    padding: 5px 5px 0;
    box-sizing: border-box;
    overflow: hidden;
  }
}
body[os='linux'] {
  #app {
    padding: 5px 5px 0;
    box-sizing: border-box;
    overflow: hidden;
  }
}
.win-drag {
  -webkit-app-region: drag;
}
#contentWrapper {
  box-sizing: content-box;
  overflow: hidden;
  position: relative;
  transition:
    border 300ms linear,
    box-shadow 150ms linear;
  &::-webkit-scrollbar {
    position: relative;
    z-index: 99999;
    height: 6px;
    width: 6px;
  }
  // 滚动条轨道
  &::-webkit-scrollbar-track {
    background-color: #ccc;
  }
  // 滚动条滑块
  &::-webkit-scrollbar-thumb {
    background-color: rgba(#ff0000, 0.6);
  }
}
#content {
  position: relative;
  width: 1px;
  height: 1px;
  opacity: 0;
}
#right-action,
#right-bottom-action {
  position: absolute;
  right: 2px;
  z-index: 9999;
  -webkit-app-region: no-drag;
}
#right-action {
  top: 2px;
  .scale-action {
    background: #ea4300;
    color: #ffffff;
    padding: 2px 4px;
    font-size: 10px;
    border-radius: 10px;
    -webkit-app-region: no-drag;
    cursor: pointer !important;
  }
  .ding-action {
    width: 16px;
    height: 16px;
    background: #c3c3c3;
    border-radius: 50%;
    color: #ffffff !important;
    -webkit-app-region: no-drag;
    &.active {
      background: #e67e22;
    }
  }
}
#ocrResult {
  position: absolute;
  top: 0;
  left: 0;
  color: var(--text-color);
  z-index: 999;
  background: var(--main-background);
  width: 100%;
  height: 100%;
  display: none;
  max-width: 100%;
  max-height: 100%;
  overflow: auto;
  > div {
    border: none;
    background: none;
    width: 100%;
    height: 100%;
    padding: 6px;
    outline: none;
  }
}

#right-bottom-action {
  bottom: 4px;
  .btn {
    -webkit-app-region: no-drag;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    cursor: pointer;
    background-color: rgba(89, 89, 89, 0.6);
    font-size: 14px;
    transition: background-color 280ms linear;
    color: #ffffff;
    border-radius: 50%;
    &:hover {
      background-color: rgba(89, 89, 89, 0.8);
    }
  }
}
</style>
