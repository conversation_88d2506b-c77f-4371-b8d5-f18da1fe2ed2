<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import OcrSettingModal from '@/views/toolSetting/templates/OcrSetting/OcrSettingModal/OcrSettingModal.vue'
import type {
  ILocalOcrModalInstance,
  IOcrSettingModalInstance
} from '@/views/toolSetting/templates/OcrSetting/OcrSettingModal'
import { getOcrPlatformInfoList } from '@/leaferApp/ocr'
import EnvironmentUtils from '@/utils/EnvironmentUtils'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { SYSTEM_OCR_LIST } from './OcrConstants'
import LocalOcrModal from '@/views/toolSetting/templates/OcrSetting/OcrSettingModal/LocalOcrModal.vue'
import {
  getInstalledLocalOcrModelList,
  type ILocalOcrModelItem
} from '@/views/toolSetting/templates/OcrSetting/LocalOcrData'
import DataUtils from '@/utils/DataUtils'

const toolConfigStore = useToolConfigStore()
const { getOcrCloudConfig, ocrCloudRemoveConfig } = useToolConfigStore();
const orcSelectListData = computed(() => {
  console.log('getOcrCloudConfig', getOcrCloudConfig)
  return [
    ...SYSTEM_OCR_LIST,
    ...getOcrCloudConfig,
    {
      key: 0,
      platform: '',
      name: '',
    },
  ]
});
function handleSpanMethod(columnIndex: number, record: {key: number}) {
  if (columnIndex === 0 && record.key === 0) {
    console.log(record)
    return {
      colspan: 5
    }
  }
}
function handleOcrConfigDelete(key: number) {
  ocrCloudRemoveConfig(key)
}

const ocrSettingModalRef = ref<IOcrSettingModalInstance>();
function handleOpenInfo(record: any) {
  ocrSettingModalRef.value.onShow(record);
}


const ocrPlatformMap = computed<Record<string, string>>(() => {
  return getOcrPlatformInfoList().reduce((obj, cur) => {
    obj[cur.code] = cur.name;
    return obj;
  }, {} as Record<string, string>);
});

function handleSelect(key: number) {
  console.log(handleSelect)
  toolConfigStore.setOcrConfig({
    cloudConfigKey: key,
  })
}

onMounted(() => {
  refreshInstalledLocalOcrModel();
});
const localOcrModalRef = ref<ILocalOcrModalInstance>();
const installedLocalOcrModelList = ref<ILocalOcrModelItem[]>([]);
function refreshInstalledLocalOcrModel() {
  installedLocalOcrModelList.value = getInstalledLocalOcrModelList();
  if (!DataUtils.getDataOcrActiveModel() && installedLocalOcrModelList.value.length) {
    // 当前使用的模型被卸载了并且安装模型列表中还有模型选择列表第一个模型
    handleEnableLocalOcrModel(installedLocalOcrModelList.value[0].installDir);
  } else {
    refreshLocalOcrEnable();
  }
}
// 卸载 Ocr 模型
function handleUninstallOcrModel(model: ILocalOcrModelItem) {
  EnvironmentUtils.uninstallOcrModel(model);
  refreshInstalledLocalOcrModel();
}
const activeModel = ref()
// 刷新激活
function refreshLocalOcrEnable() {
  activeModel.value = DataUtils.getDataOcrActiveModel();

}
function handleEnableLocalOcrModel(installDir: string) {
  window.fs.writeFileSync(window.path.join(DataUtils.getDataOcrPath(), 'enable'), installDir, 'utf-8');
  refreshLocalOcrEnable();
}
</script>
<template>
  <LocalOcrModal ref="localOcrModalRef"
                 @hide="refreshInstalledLocalOcrModel" />
  <OcrSettingModal ref="ocrSettingModalRef" />
  <a-form-item label="文字识别 (OCR)">
    <template #extra>
      <div v-if="toolConfigStore.ocrConfig.globalType === 'plugIn'">
        <div>
          <a-select size="mini"
                    style="width: 200px;"
                    v-model:model-value="toolConfigStore.ocrConfig.plugInKeyWord">
            <a-option value="OCR 文字识别|OCR文字识别">
              OCR 文字识别插件
            </a-option>
            <a-option value="讯飞ocr|讯飞ocr">
              讯飞ocr插件
            </a-option>
          </a-select>
        </div>
        <div class="u-mt10 u-fx u-f-between" style="width: 200px;">
          <div>
            识别后关闭截图
          </div>
          <a-switch v-model:model-value="toolConfigStore.ocrConfig.plugInAutoClose"
                    checked-color="#2ecc71"
                    size="small">
            <template #checked-icon>
              <icon-check/>
            </template>
            <template #unchecked-icon>
              <icon-close/>
            </template>
          </a-switch>
        </div>
      </div>
      <div v-if="toolConfigStore.ocrConfig.globalType === 'cloud'">
        <div class="u-fx u-f-between u-mb10">
          <div></div>
          <div class="u-fx u-fac u-gap10">
            <div>自动识别</div>
            <a-switch  v-model:model-value="toolConfigStore.ocrConfig.autoOcr"
                       checked-color="#2ecc71"
                       style="width: 46px">
              <template #checked-icon>
                <icon-check/>
              </template>
              <template #unchecked-icon>
                <icon-close/>
              </template>
            </a-switch>
          </div>
        </div>
        <a-table size="small"
                 style="width: 100%"
                 :data="orcSelectListData as any"
                 :pagination="false"
                 :span-method="({record, columnIndex}: any) => handleSpanMethod(columnIndex as number, record as any)">
          <template #columns>
            <a-table-column title="选择" :width="64">
              <template #cell="{record}">
                <div v-if="record.key === 0" style="width: 100%;">
                  <a-button style="width: 100%;"
                            size="mini"
                            @click="() => ocrSettingModalRef.onShow()">
                    <template #icon>
                      <icon-plus />
                    </template>
                    新增
                  </a-button>
                </div>
                <a-radio v-else
                         :model-value="toolConfigStore.ocrConfig.cloudConfigKey === record.key"
                         @change="() => handleSelect(record.key)">
                </a-radio>
              </template>
            </a-table-column>
            <a-table-column title="平台">
              <template #cell="{record}">
                {{ocrPlatformMap[record.platform]}}
              </template>
            </a-table-column>
            <a-table-column title="名称" data-index="name">
            </a-table-column>
            <a-table-column title="操作" :width="100" align="left">
              <template #title>
                <div class="u-fx u-fc">
                  操作
                </div>
              </template>
              <template #cell="{record}">
                <a-link type="text" class="font-size-smail"
                        v-if="record.key > 0"
                        @click="() => handleOpenInfo(record)">
                  详情
                </a-link>
                <a-link type="text" class="font-size-smail"
                        size="mini"
                        status="danger"
                        v-if="record.key > 0" @click="() => handleOcrConfigDelete(record.key)">
                  删除
                </a-link>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
      <div v-if="toolConfigStore.ocrConfig.globalType === 'local'">
        <div>
          <div class="u-fx u-fac u-f-between u-mb10">
            <div class="local-title">模型已安装列表</div>
            <div class="u-fx u-fac u-gap10">
              <div class="u-fx u-fac u-gap10">
                <div>自动识别</div>
                <a-switch v-model:model-value="toolConfigStore.ocrConfig.autoOcr"
                          checked-color="#2ecc71"
                          style="width: 46px">
                  <template #checked-icon>
                    <icon-check/>
                  </template>
                  <template #unchecked-icon>
                    <icon-close/>
                  </template>
                </a-switch>
              </div>
              <a-button size="mini"
                        type="primary"
                        shape="round"
                        @click="() => localOcrModalRef.show()">
                <template #icon>
                  <icon-download />
                </template>
                模型下载
              </a-button>
            </div>
          </div>
          <a-table size="small"
                   style="width: 100%"
                   :data="installedLocalOcrModelList"
                   :pagination="false">
            <template #columns>
              <a-table-column title="启用" :width="64">
                  <template #cell="{record}">
                    <a-radio :model-value="record.installDir === activeModel"
                             @click="handleEnableLocalOcrModel(record.installDir)">
                    </a-radio>
                  </template>
              </a-table-column>
              <a-table-column title="模型" data-index="modelName">
              </a-table-column>
              <a-table-column title="操作" :width="150">
                <template #cell="{record}">
                  <a-button shape="round"
                            size="mini"
                            status="danger"
                            type="text"
                            @click="handleUninstallOcrModel(record)">
                    <template #icon>
                      <icon-minus-circle />
                    </template>
                    卸载
                  </a-button>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </div>
    </template>
    <a-radio-group v-model:model-value="toolConfigStore.ocrConfig.globalType">
      <a-tooltip content="会将图片发送给其他插件实现 OCR 识别">
        <a-radio value="plugIn">utools 插件支持</a-radio>
      </a-tooltip>
      <a-radio value="cloud">云平台 OCR</a-radio>
      <a-radio value="local">本地 OCR</a-radio>
    </a-radio-group>
  </a-form-item>
</template>
<style lang="less" scoped>
.download-trigger {
  flex-direction: column;
  background: var(--main-background);
  padding: 14px;
  border-radius: 12px;
}
.local-title {
  color: var( --text-color);
  font-size: 12px;
}
</style>
