import { BaseUseTool } from '@/components/toolbox/BaseUseTool'
import ScreenshotCapacity from '@/leaferApp/ScreenshotCapacity'
import { h, render } from 'vue'
import { Button } from '@arco-design/web-vue'
import { useSingleLoading } from '@/hooks/useLoading'
import EnvironmentUtils from '@/utils/EnvironmentUtils'
import DataUtils from '@/utils/DataUtils'
import QRUtils from '@/utils/QRUtils'
import QrCodeDisplay from '@/components/toolbox/QrCodeTool/QrCodeDisplay.vue'

export class QrCodeTool extends BaseUseTool {

  constructor() {
    super()
  }

  protected async doUse() {
    if (!EnvironmentUtils.isQrCode()) {
      await this.installQrCode();
    }
    const base64 = await ScreenshotCapacity.exportScreenshotDataUrl(this.getApp());
    const res = await QRUtils.getQrCode(base64);

    const qrCode = document.getElementById('qrcode');
    qrCode.innerHTML = '';

    if (!res.data || !res.data.length) {
      ScreenshotCapacity.showTipsNotification('info', {
        title: '二维码识别提示',
        content: '当前图片识别不到二维码',
      });
      return;
    }
    console.log('二维码识别', res.data);

    for (let i = 0; i < res.data.length; i++) {
      const point = res.points[i];
      const content = res.data[i];
      const formatPoint = this.formatBounds(point);
      const element = document.createElement('div');
      const vNode = h(QrCodeDisplay, {
        ...formatPoint,
        content
      });
      render(vNode, element);
      qrCode.appendChild(element);
    }
  }

  private formatBounds(points: number[]): {left: number; top: number; width: number; height: number} {
    const pointList: {x: number; y: number;}[] = [];
    let idx = 0;
    for (let i = 0; i < points.length; i+=2) {
      pointList[idx++] = {
        x: points[i],
        y: points[i+1]
      };
    }
    const devicePixelRatio = window.devicePixelRatio;
    return {
      left: (pointList[0].x + 10) / devicePixelRatio,
      top: (pointList[0].y + 10) / devicePixelRatio,
      width: (pointList[1].x - pointList[0].x) / devicePixelRatio,
      height: (pointList[3].y - pointList[0].y) / devicePixelRatio,
    };
  }

  public installQrCode(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const [handleInstallQrCode, loading] = useSingleLoading(async () => {
        await EnvironmentUtils.downloadResource(DataUtils.getQrCodePath(), 'qrcode:wechat');
        notification.close();
        resolve(true);
      });
      const notification = ScreenshotCapacity.showConfirmNotification('info', {
        id: 'install-qrcode',
        class: 'notification-tips',
        title: '安装二维码组件提示',
        content: () => h('div', {}, [
          h('div', {}, '二维码组件按需安装(不是所有人都需要)'),
          h('div', { class: 'u-fx u-mt10', style: { justifyContent: 'flex-end' } },
            h(Button, {
              size: 'mini',
              shape: 'round',
              type: 'primary',
              loading: loading.value,
              onClick: async () => {
                await handleInstallQrCode()
              }}, '安装'))
        ]),
      });
    });
  }

  protected doDestroy() {
    const qrCode = document.getElementById('qrcode');
    qrCode.innerHTML = '';
  }
}
