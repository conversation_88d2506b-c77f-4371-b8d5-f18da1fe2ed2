import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import KeyboardsConstants from '@/core/constants/KeyboardsConstants'
import { useKeyboardConfigStore } from '@/stores/KeyboardConfigStore/KeyboardConfigStore'
import { type App, Rect } from 'leafer-ui'
import <PERSON><PERSON><PERSON>elper from '@/leaferApp/LeaferHelper'
import type { IExportOptions } from '@leafer-ui/interface'
import { Notification } from '@arco-design/web-vue'
import type { NotificationConfig } from '@arco-design/web-vue/es/notification/interface'
import DataUtils from '@/utils/DataUtils'

function switchToolBarRegisterShortcut() {
  const keyboardConfigStore = useKeyboardConfigStore();
  const config = keyboardConfigStore.getDefaultFuncKeyboardConfig(KeyboardsConstants.SWITCHOVER_TOOLBAR);
  const currentToolBoxStore = useCurrentToolBoxStore();
  window.keyboardManager.addKeyboardConfig(KeyboardsConstants.SWITCHOVER_TOOLBAR, config, {
    downHandle: () => {
      const toolboxContainer = document.getElementById('toolboxContainer')
      if (toolboxContainer.style.opacity === '1') {
        currentToolBoxStore.setCurrentToolBoxByCode('screenshotDrag');
      }
      if (toolboxContainer) {
        setToolBarShow(toolboxContainer.style.opacity !== '1');
      }
    }
  });
}

function setToolBarShow(show: boolean) {
  const currentToolBoxStore = useCurrentToolBoxStore();
  const toolboxContainer = document.getElementById('toolboxContainer')
  if (!toolboxContainer) {
    return;
  }
  if (show) {
    toolboxContainer.style.pointerEvents = 'auto'
    toolboxContainer.style.opacity = '1';
    currentToolBoxStore.enableToolBar();
  } else {
    toolboxContainer.style.pointerEvents = 'none';
    toolboxContainer.style.opacity = '0';
    currentToolBoxStore.disableToolBar();
  }
  LeaferHelper.autoAdjustWindowSize(currentToolBoxStore.getLeaferApp())
}



function exportScreenshotImage(app: App, options: IExportOptions = {}, fileFormat: 'png' | 'jpg' = 'png'): Promise<string | Blob> {
  app.editor.cancel();
  return new Promise((resolve, reject) => {
    const ratio = window.devicePixelRatio;
    if (window.environment === 'capture') {
      const {left, top, width, height} = LeaferHelper.getCaptureRectBounds(app);
      app.export('png', {
        ...options,
        quality: 1,
        screenshot: {
          x: left * ratio, y: top * ratio,
          width, height,
        } }).then(async (res) => {
        resolve(res.data);
      });
    } else if (window.environment === 'screenshot') {
      // const ratio = window.devicePixelRatio;
      const ground = app.ground.children[0].clone() as Rect;
      ground.set({
        width: app.width,
        height: app.height,
        x: 0,
        y: 0,
      })
      // app.tree.addAt(ground, 0);
      app.export(fileFormat, {
        ...options,
        trim: true,
        quality: 1,
        pixelRatio: ratio,
        screenshot: true,
      }).then(res => {
        resolve(res.data);
      })
    }
  });
}


function exportScreenshotDataUrl(app: App, options: IExportOptions = {}): Promise<string> {
  return exportScreenshotImage(app, options) as Promise<string>;
}


function exportScreenshotBlob(app: App, options: IExportOptions = {}, format: 'png' | 'jpg' = 'png'): Promise<Blob> {
  return exportScreenshotImage(app, {...options, blob: true}, format) as Promise<Blob>;
}


function showNotification(type: 'info' | 'error' | 'warning' | 'success', config: NotificationConfig, noticeType: 'tips' | 'confirm' = 'tips') {
  return Notification[type]({
    class: 'notification-tips',
    position: 'topLeft',
    duration: noticeType === 'tips' ? 3000 : 60 * 60 * 1000,
    closable: true,
    ...config
  })
}

function showTipsNotification(type: 'info' | 'error' | 'warning' | 'success', config: NotificationConfig) {
  return showNotification(type, config, 'tips');
}
function showConfirmNotification(type: 'info' | 'error' | 'warning' | 'success', config: NotificationConfig) {
  return showNotification(type, config, 'confirm');
}

/**
 * 截图关闭
 */
async function screenshotClose() {
  window.winHelper.getCurrentWindow().setTitle('截图关闭');
  window.winHelper.getCurrentWindow().hide();
  const { getLeaferApp } = useCurrentToolBoxStore();
  const app = getLeaferApp();
  if (window.globalParams.id) {
    // 存储本次截图
    if (window.environment === 'screenshot') {
      // 截图
      const base64 = await exportScreenshotDataUrl(app);
      console.log('保存图片', base64)
      const fileId = window.globalParams.id;
      console.log(app);
      const fileDirPath = window.path.join(DataUtils.getDataScreenshotLibrary(), fileId);
      console.log('fileDirPath', fileDirPath);
      const ext = base64.match(/^data:image\/(\w+)\+?\w*;base64,/);
      const suffix = ext && ext.length > 1 ? ext[1] : 'png';

      window.fs.writeFileSync(window.path.join(fileDirPath, 'edit.' + suffix),
        base64.replace(/^data:image\/\w+;base64,/, ""),
        'base64');
      const treeData = app.tree.toJSON();
      window.fs.writeFileSync(window.path.join(fileDirPath, 'edit.json'), JSON.stringify(treeData), 'utf-8');
      const groupData = app.ground.toJSON();
      window.fs.writeFileSync(window.path.join(fileDirPath, 'ground.json'), JSON.stringify(groupData), 'utf-8');
    }
  }
  setTimeout(() => {
    // 关闭
    window.winHelper.getCurrentWindow().close();
  }, 1200);
}


export default {
  switchToolBarRegisterShortcut,
  setToolBarShow,
  exportScreenshotDataUrl,
  exportScreenshotBlob,
  showTipsNotification,
  showConfirmNotification,
  screenshotClose,
}
