// 最优先加载文件
import './init/CaptureContainerInit'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import CaptureContainer from './CaptureContainer.vue'
import ColorPickers from 'vue3-colorpicker'
import jsUtil from '@/utils/jsUtil'
import KeyboardManager from '@/utils/KeyboardManager'
import { useTheme } from '@/hooks/useTheme'
import '@leafer-in/find'
import '@leafer-in/export'
import '@leafer-in/viewport'
import 'vue3-colorpicker/style.css'
import '@/assets/less/theme.less'
import '@/assets/captureContainer.less'
import DataUtils from '@/utils/DataUtils'

// @ts-ignore
window.Module = {
  wasmBinaryFile: window.path.join(DataUtils.getOpenCVPath(), 'opencv.wasm'),
}
jsUtil.loadScript(window.path.join(DataUtils.getOpenCVPath(), 'opencv.js'));

const app = createApp(CaptureContainer)
app.use(createPinia())
app.use(ColorPickers)
app.mount('#app');
window.keyboardManager = new KeyboardManager();

useTheme({
  setDarkTheme: () => {
    document.body.setAttribute('arco-theme', 'dark')
  },
  setDefaultTheme: () => {
    document.body.removeAttribute('arco-theme');
  }
});
