<script setup lang="ts">
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import { CONFIG } from './index'
import { ref } from 'vue'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'
import type TranslateTool from './TranslateTool'


const showStatus = ref(true);

function handleSwitchover() {
  document.getElementById('translate').style.display = showStatus.value ? 'none' : 'block';
  showStatus.value = !showStatus.value;
}

const currentToolBoxStore = useCurrentToolBoxStore()

function handleReload() {
  const tool = currentToolBoxStore.getToolByCode<TranslateTool>(CONFIG.code);
  const ocrResultInner = document.getElementById('translate');
  ocrResultInner.innerHTML = '';
  tool.translate();
}
</script>

<template>
  <BaseToolBox v-bind="CONFIG">
    <template #config>
      <div class="u-fx u-fac u-fc"
           style="padding: 4px 8px; flex-direction: column;">
        <div style="margin-bottom: 6px;">
          <t-button size="small"
                    theme="default"
                    @click="handleSwitchover">
            {{showStatus ? '隐藏' : '显示'}}文本
          </t-button>
        </div>
        <div>
          <t-button size="small"
                    theme="default"
                    @click="handleReload">
            重新识别
          </t-button>
        </div>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">

</style>
