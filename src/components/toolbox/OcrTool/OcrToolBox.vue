<script setup lang="ts">
import BaseToolBox from '@/components/toolbox/BaseToolBox.vue'
import type { ToolboxInfoItemConfig } from '@/components/toolbox'
import { CONFIG } from './index'
import HtmlDomConstants from '@/core/constants/HtmlDomConstants'
import { ref } from 'vue'
import type OcrTool from '@/components/toolbox/OcrTool/OcrTool'
import { useCurrentToolBoxStore } from '@/stores/CurrentToolBoxStore'

const toolInfo: ToolboxInfoItemConfig = CONFIG;


const showStatus = ref(false);
function handleSwitchoverText() {
  const element = document.getElementById(HtmlDomConstants.ToolOcrResult);
  const ocrResultInner = document.getElementById(HtmlDomConstants.ToolOcrResultInner);
  if (!element.style.display) {
    element.style.display = 'none';
  }
  if (!ocrResultInner.innerHTML) {
    return;
  }
  console.log('element.style.display', element.style.display)
  showStatus.value =  element.style.display === 'none';
  element.style.display = element.style.display === 'none' ? 'block' : 'none';
}
const currentToolBoxStore = useCurrentToolBoxStore()

function handleReloadOcr() {
  const tool = currentToolBoxStore.getToolByCode<OcrTool>(toolInfo.code);
  const ocrResultInner = document.getElementById('ocr');
  ocrResultInner.innerHTML = '';
  tool.ocr();
}
</script>

<template>
  <BaseToolBox v-bind="toolInfo">
    <template #config>
      <div class="u-fx u-fac u-fc"
           style="padding: 4px 8px; flex-direction: column;">
        <div style="margin-bottom: 6px;">
          <a-button size="mini" @click="handleSwitchoverText">
            {{showStatus ? '隐藏' : '显示'}}文本
          </a-button>
        </div>
        <div>
          <a-button size="mini"
                    @click="handleReloadOcr">
            重新识别
          </a-button>
        </div>
      </div>
    </template>
  </BaseToolBox>
</template>

<style scoped lang="less">

</style>
