@import './base.css';
@import "./less/theme.less";
@import "@xiaou66/u-web-ui/dist/u-web-ui.css";
body {
  width: 100vw;
  height: 100vh;
  color: var(--text-color);
}
div[contenteditable=true] {
  color: #0E0E0E;
}

.notification-tips {
  width: 120% !important;
  padding: 10px;
  .arco-checkbox {
    padding-left: 0;
    .arco-checkbox-label {
      font-size: 12px;
    }
  }
  .arco-notification-left {
    padding-right: 6px;
  }
  .arco-notification-title {
    font-size: 14px;
  }
  .arco-notification-content {
    font-size: 12px;
  }
}
.u-web-popup {
  .t-popup__content {
    padding: 0 !important;
    margin: 0 !important;
  }
}
