<script setup lang="ts">
import { onMounted, ref } from 'vue'
import PictureBedPlus from '@/api/service/PictureBedPlus'
import type { CascaderNode } from '@arco-design/web-vue/es/cascader/interface'
import { NotifyPlugin } from 'tdesign-vue-next'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'


const props = defineProps<{
  sceneCode: string
}>();


const { getSceneImageUploadConfig } = useToolConfigStore();

const imageUploadConfig = getSceneImageUploadConfig(props.sceneCode);

function handleGoPictureBed() {
  utools.redirect(['图床 Plus'], null);
}

const uploadWayList = ref<CascaderNode[]>([] as CascaderNode[]);
async function refreshUploadWayList(manual = false) {
  try {
    uploadWayList.value = await PictureBedPlus.getUploadWayList();
    if (manual) {
      await NotifyPlugin.success({
        title: "提示",
        content: "刷新成功",
        duration: 1000
      })
    }
  }catch  {
    if (manual) {
      await NotifyPlugin.warning({
        title: "提示",
        content: "图床 Plus 本地服务未启动",
        duration: 1000
      })
    }
  }
  console.log('uploadWayList.value', uploadWayList.value)
}
onMounted(() => {
  refreshUploadWayList();
})
</script>

<template>
  <div>
    <t-form>
      <t-form-item label="图床配置">
        <t-radio-group v-model:value="imageUploadConfig.imageUploadType"
                       size="small"
                       variant="default-filled"
                       type="button">
          <t-tooltip>
            <template #content>
              优先使用图床 Plus 的本地服务, 其次使用跳转插件上传
            </template>
            <t-radio-button value="auto">智能模式</t-radio-button>
          </t-tooltip>
          <t-tooltip>
            <template #content>
              自动打开图床上传截图
            </template>
            <t-radio-button value="plugIn">跳转插件</t-radio-button>
          </t-tooltip>
        </t-radio-group>
        <template #help>
          <div>
            <div>
              图床功能需要
              <t-link size="small" theme="primary" @click="handleGoPictureBed">
                图床 Plus 插件
              </t-link>
              才可以使用
            </div>
          </div>
        </template>
      </t-form-item>
      <t-form-item label="上传方式">
        <template #help>仅本地服务方式调用生效</template>
        <div class="flex gap-1">
          <t-cascader v-model:model-value="imageUploadConfig.uploadWay"
                      :options="uploadWayList"
                      :style="{width:'220px'}"
                      placeholder="选择上传方式"
                      size="small"
                      allow-search>
          </t-cascader>
          <t-button size="small"
                    @click="() => refreshUploadWayList(true)"
                    theme="default">
            <template #icon>
              <t-icon class="i-u-refresh"></t-icon>
            </template>
            刷新
          </t-button>
        </div>
      </t-form-item>
    </t-form>
  </div>
</template>

<style scoped lang="less">

</style>
