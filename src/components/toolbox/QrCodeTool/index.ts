import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import { QrCodeTool } from './QrCodeTool'
import QrCodeToolBox from './QrCodeToolBox.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'qrcode',
  title: '二维码',
  sort: 4,
  group: 'tool',
  keyboard: {
    key: ''
  },
  toolIcon: 'i-u-pay-code-one',
  config: false,
}

export default {
  info: CONFIG,
  tool: new QrCodeTool(),
  component: async () => QrCodeToolBox,
} as ToolboxItem;
