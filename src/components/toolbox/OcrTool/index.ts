import type { ToolboxInfoItemConfig, ToolboxItem } from '@/components/toolbox'
import OcrTool from '@/components/toolbox/OcrTool/OcrTool'
import OcrToolBox from '@/components/toolbox/OcrTool/OcrToolBox.vue'
import OcrToolSetting from '@/components/toolbox/OcrTool/OcrToolSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'ocr',
  title: '识别',
  sort: 1,
  group: 'tool',
  keyboard: {
    ctrl: false,
    key: ''
  },
  toolIcon: 'i-u-scanning',
  config: true,
  sceneSetting: async () => OcrToolSetting,
}

export default {
  info: CONFIG,
  tool: new OcrTool(),
  component: async () => OcrToolBox,
} as ToolboxItem;
