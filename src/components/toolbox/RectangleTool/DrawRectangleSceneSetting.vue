<script setup lang="ts">
import StrokeSelectPanel from '@/components/panel/StrokeSelectPanel.vue'
import { useToolConfigStore } from '@/stores/ToolConfigStore/ToolConfigStore'
import { ref } from 'vue'

const props = defineProps<{
  sceneCode: string
}>();

const toolConfigStore = useToolConfigStore();

const modal = ref({});
const sceneRectangleConfig = toolConfigStore.getSceneRectangleConfig(props.sceneCode);
console.log(sceneRectangleConfig.value)
</script>

<template>
  <div class="toolbox-config">
    <div class="scene-setting-form" v-if="sceneRectangleConfig">
      <t-form :model="modal" :label-width="80">
        <t-form-item label="边框大小">
          <t-input-number v-model:value="sceneRectangleConfig.strokeWidth"
                          theme="column" />
        </t-form-item>
        <t-form-item label="颜色">
          <StrokeSelectPanel  v-model:stroke-select-options="sceneRectangleConfig.strokeSelectOptions"
                              v-model:model-value="sceneRectangleConfig.currentStroke"
                              hide-label />
        </t-form-item>
        <t-form-item label="是否填充">
          <t-checkbox v-model:checked="sceneRectangleConfig.fill" />
        </t-form-item>
        <t-form-item label="形状">
          <t-radio-group v-model:value="sceneRectangleConfig.shape"
                         variant="default-filled"
                         size="small"
                         type="button">
            <t-radio-button value="normal">正常</t-radio-button>
            <t-radio-button value="round">圆角</t-radio-button>
          </t-radio-group>
        </t-form-item>
      </t-form>
    </div>
  </div>
</template>

<style scoped lang="less">
.scene-setting-form {
  width: 70%;
}
</style>
