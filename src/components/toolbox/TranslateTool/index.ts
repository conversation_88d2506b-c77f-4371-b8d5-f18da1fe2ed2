import type { ToolboxInfoItemConfig, ToolboxItem } from '../../toolbox'
import TranslateTool from './TranslateTool'
import TranslateToolBox from './TranslateToolBox.vue'
import TranslateSceneSetting from './TranslateSceneSetting.vue'
//@unocss-include
export const CONFIG: ToolboxInfoItemConfig = {
  code: 'translate',
  title: '翻译',
  sort: 1.5,
  group: 'tool',
  keyboard: {
    key: ''
  },
  toolIcon: 'i-u-translate',
  config: true,
  sceneSetting: async () => TranslateSceneSetting,
}

export default {
  info: CONFIG,
  tool: new TranslateTool(),
  component: async () => TranslateToolBox
} as ToolboxItem;
