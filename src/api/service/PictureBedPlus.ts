import type { CascaderNode } from '@arco-design/web-vue/es/cascader/interface'
import { ServiceClient } from '@xiaou66/interconnect-client'


function getClient() {
  // 命名管道的路径
  const pipeName = window.path.join(utools.getPath('userData'), '.pip','picture-bed-plus');
  return new ServiceClient(window.net, pipeName, import.meta.env.VITE_NAME)
}

/**
 * 获取所有上传方式
 */
async function getUploadWayList() {
  return await getClient().callServiceMethod<CascaderNode[]>('service.info.getUploadWayList');
}

export interface IUploadFileParams {
  /**
   * 插件名称
   */
  pluginName?: string,
  uploadWay?: string;
  filePath?: string;
  base64?: string;
}
async function getImageUploadSync(params: IUploadFileParams) {
  return await getClient().callServiceMethod('service.upload.file.sync', params);
}
export default {
  getUploadWayList,
  getImageUploadSync
}
