import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { useTheme } from '@/hooks/useTheme'
import WinHelper from '@/core/utils/WinHelper'
import ScreenshotContainer from '@/ScreenshotContainer.vue'
import { initScreenshotApiProvider } from '@/core/sdk/screenshotApiProvider'
import ColorPickers from 'vue3-colorpicker'
import KeyboardManager from '@/utils/KeyboardManager'
import '@leafer-in/find'
import '@leafer-in/export'
import '@leafer-in/viewport'
import 'vue3-colorpicker/style.css'
// 导入Arco Design样式
import 'tdesign-vue-next/es/style/index.css';

import './assets/screenshotContainer.less'
import utoolsImage from '@/directives/utoolsImage'
;(() => {
  import('virtual:uno.css')
})()

// 初始化 API 容器
window.api = {}
window.tempData = {}
window.globalParams = {}

window.keyboardManager = new KeyboardManager()

if (window.utools) {
  document.body.setAttribute(
    'os',
    utools.isMacOS() ? 'mac' : utools.isWindows() ? 'windows' : 'linux',
  )

  window.winHelper = new WinHelper({
    initCallback(initData) {
      initScreenshotApiProvider()
    },
  })
}

const app = createApp(ScreenshotContainer)

app.use(createPinia())
app.use(ColorPickers).use(utoolsImage)

app.mount('#app')

useTheme({
  setDarkTheme: () => {
    document.body.setAttribute('arco-theme', 'dark')
  },
  setDefaultTheme: () => {
    document.body.removeAttribute('arco-theme')
  },
})

// hotkeys('*', function() {
//   console.log(hotkeys.getPressedKeyString()); //=> ['⌘', '⌃', '⇧', 'A', 'F']
//   // utools.showNotification(hotkeys.getPressedKeyString().join("+"))
// })
